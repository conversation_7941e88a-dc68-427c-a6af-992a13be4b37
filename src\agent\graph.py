"""LangGraph chatbot graph with checkpointing.

A simple chatbot that uses OpenRouter LLM via llm_factory with conversation persistence.
"""

from __future__ import annotations

import sys
from pathlib import Path

# Add the src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir.parent
if str(src_dir) not in sys.path:
    sys.path.insert(0, str(src_dir))

from typing import TypedDict
from langchain_core.messages import AIMessage
from langgraph.graph import StateGraph
from langgraph.checkpoint.memory import MemorySaver

from agent.nodes import call_model
from agent.nodes.tools_node import create_tools_node
from agent.state import State


class Configuration(TypedDict):
    """Configurable parameters for the chatbot."""
    model_name: str
    system_prompt: str


def conditional_edge_1(state: State) -> str:
    """Determine whether to route to tools or end the conversation.

    Args:
        state: The current state containing messages

    Returns:
        "tools" if the last AI message has tool calls, "__end__" otherwise
    """
    messages = state.get("messages", [])

    if not messages:
        return "__end__"

    last_message = messages[-1]
    print(f"Last message: {last_message}")
    # Check if the last message is an AI message with tool calls
    if isinstance(last_message, AIMessage) and last_message.tool_calls:
        return "tools"

    return "__end__"


# Create checkpointer for conversation persistence
checkpointer = MemorySaver()

# Define the graph with checkpointing
graph = (
    StateGraph(State, config_schema=Configuration)
    .add_node("call_model", call_model)
    .add_node("tools", create_tools_node)
    .add_edge("__start__", "call_model")
    .add_conditional_edges("call_model", conditional_edge_1)
    .add_edge("tools", "call_model")
    .compile()
)


